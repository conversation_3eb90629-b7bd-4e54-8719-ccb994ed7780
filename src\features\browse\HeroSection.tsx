import React from 'react';
import ImageWithFallback from '../../components/ui/ImageWithFallback';
import { buildImageUrl, getTitle } from '../../utils/tmdb';
import { useGetTrendingQuery } from '../../services/tmdb';

const HeroSection: React.FC = () => {
  const { data: trendingData, isLoading, isError, refetch } = useGetTrendingQuery();
  const featuredItem = trendingData?.results[0]; // Get the first trending item

  if (isLoading) {
    return <div className="h-96 bg-gray-700 animate-pulse" />;
  }

  if (isError) {
    return (
      <div className="h-96 bg-gray-800 text-center text-white flex flex-col items-center justify-center p-6">
        <h2 className="text-2xl font-bold mb-4 text-red-500">API Connection Error</h2>
        <p className="mb-2">Failed to load featured content from TMDB.</p>
        <p className="text-sm text-gray-300 mb-4">
          This might be due to an invalid or expired API key.
        </p>
        <div className="bg-gray-700 p-4 rounded-lg mb-4 text-left">
          <p className="text-sm font-semibold mb-2">To fix this issue:</p>
          <ol className="text-sm text-gray-300 list-decimal list-inside space-y-1">
            <li>Visit <a href="https://www.themoviedb.org/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">themoviedb.org</a></li>
            <li>Create an account or log in</li>
            <li>Go to Account Settings → API</li>
            <li>Request an API key (choose "Developer")</li>
            <li>Update your .env file with the new API key</li>
            <li>Restart the development server</li>
          </ol>
        </div>
        <button
          onClick={refetch}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Retry Connection
        </button>
      </div>
    );
  }

  if (!featuredItem) {
    return (
      <div className="h-96 bg-gray-800 text-center text-white flex items-center justify-center">
        No featured content available.
      </div>
    );
  }

  const imageUrl = buildImageUrl(featuredItem.backdrop_path, 'original');
  const title = getTitle(featuredItem);
  const description = featuredItem.overview || 'No description available.';

  return (
    <div className="relative h-96 bg-gray-800">
      <ImageWithFallback
        src={imageUrl}
        alt={title}
        fallbackSrc="/placeholder.webp"
        className="w-full h-full object-cover"
      />
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
        <h1 className="text-4xl text-white">{title}</h1>
        <p className="text-white mt-2">{description}</p>
      </div>
    </div>
  );
};

export default HeroSection;
