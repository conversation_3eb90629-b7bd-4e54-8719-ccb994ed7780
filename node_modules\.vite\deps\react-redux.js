import {
  Provider_default,
  ReactReduxContext,
  connect_default,
  createDispatchHook,
  createSelectorHook,
  createStoreHook,
  import_react_dom,
  shallowEqual,
  useDispatch,
  useSelector,
  useStore
} from "./chunk-HLPUQQM3.js";
import "./chunk-LAD36NGY.js";
import "./chunk-VRHMX22Y.js";
import "./chunk-2UC5YKPU.js";
import "./chunk-UXIASGQL.js";
var export_batch = import_react_dom.unstable_batchedUpdates;
export {
  Provider_default as Provider,
  ReactReduxContext,
  export_batch as batch,
  connect_default as connect,
  createDispatchHook,
  createSelectorHook,
  createStoreHook,
  shallowEqual,
  useDispatch,
  useSelector,
  useStore
};
//# sourceMappingURL=react-redux.js.map
