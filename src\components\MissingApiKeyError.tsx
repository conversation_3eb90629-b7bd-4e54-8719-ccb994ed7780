import React from 'react';

const MissingApiKeyError: React.FC = () => (
  <div className="flex flex-col items-center justify-center min-h-screen bg-gray-900 text-white p-6">
    <h1 className="text-3xl font-bold mb-6 text-red-600">TMDB API Key Required</h1>
    <div className="max-w-2xl text-center">
      <p className="text-lg mb-6">
        The TMDB API key is missing or invalid. Please configure the{' '}
        <code className="bg-gray-800 p-1 rounded">VITE_TMDB_API_KEY</code> environment variable.
      </p>

      <div className="bg-gray-800 p-6 rounded-lg text-left mb-6">
        <h2 className="text-xl font-semibold mb-4 text-center">How to get your TMDB API Key:</h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-300">
          <li>Visit <a href="https://www.themoviedb.org/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">themoviedb.org</a></li>
          <li>Create a free account or log in</li>
          <li>Go to your Account Settings</li>
          <li>Click on the "API" section</li>
          <li>Request an API key (choose "Developer" option)</li>
          <li>Fill out the application form</li>
          <li>Copy your API Read Access Token</li>
          <li>Add it to your <code className="bg-gray-700 p-1 rounded">.env</code> file</li>
          <li>Restart the development server</li>
        </ol>
      </div>

      <div className="bg-gray-800 p-4 rounded-lg">
        <p className="text-sm text-gray-400 mb-2">Your .env file should look like:</p>
        <code className="block bg-gray-700 p-3 rounded text-green-400 text-sm">
          VITE_TMDB_API_KEY=your_actual_api_key_here
        </code>
      </div>
    </div>
  </div>
);

export default MissingApiKeyError;
