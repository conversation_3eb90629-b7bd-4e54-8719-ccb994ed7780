{"version": 3, "sources": ["../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../fast-deep-equal/index.js", "../../sister/src/sister.js", "../../load-script/index.js", "../../youtube-player/dist/loadYouTubeIframeApi.js", "../../youtube-player/node_modules/ms/index.js", "../../youtube-player/node_modules/debug/src/debug.js", "../../youtube-player/node_modules/debug/src/browser.js", "../../youtube-player/dist/functionNames.js", "../../youtube-player/dist/eventNames.js", "../../youtube-player/dist/constants/PlayerStates.js", "../../youtube-player/dist/FunctionStateMap.js", "../../youtube-player/dist/YouTubePlayer.js", "../../youtube-player/dist/index.js", "../../react-youtube/src/YouTube.tsx"], "sourcesContent": ["/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "'use strict';\n\nvar Sister;\n\n/**\n* @link https://github.com/gajus/sister for the canonical source repository\n* @license https://github.com/gajus/sister/blob/master/LICENSE BSD 3-Clause\n*/\nSister = function () {\n    var sister = {},\n        events = {};\n\n    /**\n     * @name handler\n     * @function\n     * @param {Object} data Event data.\n     */\n\n    /**\n     * @param {String} name Event name.\n     * @param {handler} handler\n     * @return {listener}\n     */\n    sister.on = function (name, handler) {\n        var listener = {name: name, handler: handler};\n        events[name] = events[name] || [];\n        events[name].unshift(listener);\n        return listener;\n    };\n\n    /**\n     * @param {listener}\n     */\n    sister.off = function (listener) {\n        var index = events[listener.name].indexOf(listener);\n\n        if (index !== -1) {\n            events[listener.name].splice(index, 1);\n        }\n    };\n\n    /**\n     * @param {String} name Event name.\n     * @param {Object} data Event data.\n     */\n    sister.trigger = function (name, data) {\n        var listeners = events[name],\n            i;\n\n        if (listeners) {\n            i = listeners.length;\n            while (i--) {\n                listeners[i].handler(data);\n            }\n        }\n    };\n\n    return sister;\n};\n\nmodule.exports = Sister;\n", "\nmodule.exports = function load (src, opts, cb) {\n  var head = document.head || document.getElementsByTagName('head')[0]\n  var script = document.createElement('script')\n\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n\n  opts = opts || {}\n  cb = cb || function() {}\n\n  script.type = opts.type || 'text/javascript'\n  script.charset = opts.charset || 'utf8';\n  script.async = 'async' in opts ? !!opts.async : true\n  script.src = src\n\n  if (opts.attrs) {\n    setAttributes(script, opts.attrs)\n  }\n\n  if (opts.text) {\n    script.text = '' + opts.text\n  }\n\n  var onend = 'onload' in script ? stdOnEnd : ieOnEnd\n  onend(script, cb)\n\n  // some good legacy browsers (firefox) fail the 'in' detection above\n  // so as a fallback we always set onload\n  // old IE will ignore this and new IE will set onload\n  if (!script.onload) {\n    stdOnEnd(script, cb);\n  }\n\n  head.appendChild(script)\n}\n\nfunction setAttributes(script, attrs) {\n  for (var attr in attrs) {\n    script.setAttribute(attr, attrs[attr]);\n  }\n}\n\nfunction stdOnEnd (script, cb) {\n  script.onload = function () {\n    this.onerror = this.onload = null\n    cb(null, script)\n  }\n  script.onerror = function () {\n    // this.onload = null here is necessary\n    // because even IE9 works not like others\n    this.onerror = this.onload = null\n    cb(new Error('Failed to load ' + this.src), script)\n  }\n}\n\nfunction ieOnEnd (script, cb) {\n  script.onreadystatechange = function () {\n    if (this.readyState != 'complete' && this.readyState != 'loaded') return\n    this.onreadystatechange = null\n    cb(null, script) // there is no way to catch loading errors in IE8\n  }\n}\n", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _loadScript = require('load-script');\n\nvar _loadScript2 = _interopRequireDefault(_loadScript);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = function (emitter) {\n  /**\n   * A promise that is resolved when window.onYouTubeIframeAPIReady is called.\n   * The promise is resolved with a reference to window.YT object.\n   */\n  var iframeAPIReady = new Promise(function (resolve) {\n    if (window.YT && window.YT.Player && window.YT.Player instanceof Function) {\n      resolve(window.YT);\n\n      return;\n    } else {\n      var protocol = window.location.protocol === 'http:' ? 'http:' : 'https:';\n\n      (0, _loadScript2.default)(protocol + '//www.youtube.com/iframe_api', function (error) {\n        if (error) {\n          emitter.trigger('error', error);\n        }\n      });\n    }\n\n    var previous = window.onYouTubeIframeAPIReady;\n\n    // The API will call this function when page has finished downloading\n    // the JavaScript for the player API.\n    window.onYouTubeIframeAPIReady = function () {\n      if (previous) {\n        previous();\n      }\n\n      resolve(window.YT);\n    };\n  });\n\n  return iframeAPIReady;\n};\n\nmodule.exports = exports['default'];", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isNaN(val) === false) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  if (ms >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (ms >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (ms >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (ms >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  return plural(ms, d, 'day') ||\n    plural(ms, h, 'hour') ||\n    plural(ms, m, 'minute') ||\n    plural(ms, s, 'second') ||\n    ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, n, name) {\n  if (ms < n) {\n    return;\n  }\n  if (ms < n * 1.5) {\n    return Math.floor(ms / n) + ' ' + name;\n  }\n  return Math.ceil(ms / n) + ' ' + name + 's';\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Previous log timestamp.\n */\n\nvar prevTime;\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  return debug;\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (var i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n", "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  'lightseagreen',\n  'forestgreen',\n  'goldenrod',\n  'dodgerblue',\n  'darkorchid',\n  'crimson'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\n\n/**\n * @see https://developers.google.com/youtube/iframe_api_reference#Functions\n */\nexports.default = ['cueVideoById', 'loadVideoById', 'cueVideoByUrl', 'loadVideoByUrl', 'playVideo', 'pauseVideo', 'stopVideo', 'getVideoLoadedFraction', 'cuePlaylist', 'loadPlaylist', 'nextVideo', 'previousVideo', 'playVideoAt', 'setShuffle', 'setLoop', 'getPlaylist', 'getPlaylistIndex', 'setOption', 'mute', 'unMute', 'isMuted', 'setVolume', 'getVolume', 'seekTo', 'getPlayerState', 'getPlaybackRate', 'setPlaybackRate', 'getAvailablePlaybackRates', 'getPlaybackQuality', 'setPlaybackQuality', 'getAvailableQualityLevels', 'getCurrentTime', 'getDuration', 'removeEventListener', 'getVideoUrl', 'getVideoEmbedCode', 'getOptions', 'getOption', 'addEventListener', 'destroy', 'setSize', 'getIframe'];\nmodule.exports = exports['default'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\n\n/**\n * @see https://developers.google.com/youtube/iframe_api_reference#Events\n * `volumeChange` is not officially supported but seems to work\n * it emits an object: `{volume: 82.6923076923077, muted: false}`\n */\nexports.default = ['ready', 'stateChange', 'playbackQualityChange', 'playbackRateChange', 'error', 'apiChange', 'volumeChange'];\nmodule.exports = exports['default'];", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = {\n  BUFFERING: 3,\n  ENDED: 0,\n  PAUSED: 2,\n  PLAYING: 1,\n  UNSTARTED: -1,\n  VIDEO_CUED: 5\n};\nmodule.exports = exports[\"default\"];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _PlayerStates = require('./constants/PlayerStates');\n\nvar _PlayerStates2 = _interopRequireDefault(_PlayerStates);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = {\n  pauseVideo: {\n    acceptableStates: [_PlayerStates2.default.ENDED, _PlayerStates2.default.PAUSED],\n    stateChangeRequired: false\n  },\n  playVideo: {\n    acceptableStates: [_PlayerStates2.default.ENDED, _PlayerStates2.default.PLAYING],\n    stateChangeRequired: false\n  },\n  seekTo: {\n    acceptableStates: [_PlayerStates2.default.ENDED, _PlayerStates2.default.PLAYING, _PlayerStates2.default.PAUSED],\n    stateChangeRequired: true,\n\n    // TRICKY: `seekTo` may not cause a state change if no buffering is\n    // required.\n    timeout: 3000\n  }\n};\nmodule.exports = exports['default'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _debug = require('debug');\n\nvar _debug2 = _interopRequireDefault(_debug);\n\nvar _functionNames = require('./functionNames');\n\nvar _functionNames2 = _interopRequireDefault(_functionNames);\n\nvar _eventNames = require('./eventNames');\n\nvar _eventNames2 = _interopRequireDefault(_eventNames);\n\nvar _FunctionStateMap = require('./FunctionStateMap');\n\nvar _FunctionStateMap2 = _interopRequireDefault(_FunctionStateMap);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/* eslint-disable promise/prefer-await-to-then */\n\nvar debug = (0, _debug2.default)('youtube-player');\n\nvar YouTubePlayer = {};\n\n/**\n * Construct an object that defines an event handler for all of the YouTube\n * player events. Proxy captured events through an event emitter.\n *\n * @todo Capture event parameters.\n * @see https://developers.google.com/youtube/iframe_api_reference#Events\n */\nYouTubePlayer.proxyEvents = function (emitter) {\n  var events = {};\n\n  var _loop = function _loop(eventName) {\n    var onEventName = 'on' + eventName.slice(0, 1).toUpperCase() + eventName.slice(1);\n\n    events[onEventName] = function (event) {\n      debug('event \"%s\"', onEventName, event);\n\n      emitter.trigger(eventName, event);\n    };\n  };\n\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n\n  try {\n    for (var _iterator = _eventNames2.default[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var eventName = _step.value;\n\n      _loop(eventName);\n    }\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n\n  return events;\n};\n\n/**\n * Delays player API method execution until player state is ready.\n *\n * @todo Proxy all of the methods using Object.keys.\n * @todo See TRICKY below.\n * @param playerAPIReady Promise that resolves when player is ready.\n * @param strictState A flag designating whether or not to wait for\n * an acceptable state when calling supported functions.\n * @returns {Object}\n */\nYouTubePlayer.promisifyPlayer = function (playerAPIReady) {\n  var strictState = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var functions = {};\n\n  var _loop2 = function _loop2(functionName) {\n    if (strictState && _FunctionStateMap2.default[functionName]) {\n      functions[functionName] = function () {\n        for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        return playerAPIReady.then(function (player) {\n          var stateInfo = _FunctionStateMap2.default[functionName];\n          var playerState = player.getPlayerState();\n\n          // eslint-disable-next-line no-warning-comments\n          // TODO: Just spread the args into the function once Babel is fixed:\n          // https://github.com/babel/babel/issues/4270\n          //\n          // eslint-disable-next-line prefer-spread\n          var value = player[functionName].apply(player, args);\n\n          // TRICKY: For functions like `seekTo`, a change in state must be\n          // triggered given that the resulting state could match the initial\n          // state.\n          if (stateInfo.stateChangeRequired ||\n\n          // eslint-disable-next-line no-extra-parens\n          Array.isArray(stateInfo.acceptableStates) && stateInfo.acceptableStates.indexOf(playerState) === -1) {\n            return new Promise(function (resolve) {\n              var onPlayerStateChange = function onPlayerStateChange() {\n                var playerStateAfterChange = player.getPlayerState();\n\n                var timeout = void 0;\n\n                if (typeof stateInfo.timeout === 'number') {\n                  timeout = setTimeout(function () {\n                    player.removeEventListener('onStateChange', onPlayerStateChange);\n\n                    resolve();\n                  }, stateInfo.timeout);\n                }\n\n                if (Array.isArray(stateInfo.acceptableStates) && stateInfo.acceptableStates.indexOf(playerStateAfterChange) !== -1) {\n                  player.removeEventListener('onStateChange', onPlayerStateChange);\n\n                  clearTimeout(timeout);\n\n                  resolve();\n                }\n              };\n\n              player.addEventListener('onStateChange', onPlayerStateChange);\n            }).then(function () {\n              return value;\n            });\n          }\n\n          return value;\n        });\n      };\n    } else {\n      functions[functionName] = function () {\n        for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n\n        return playerAPIReady.then(function (player) {\n          // eslint-disable-next-line no-warning-comments\n          // TODO: Just spread the args into the function once Babel is fixed:\n          // https://github.com/babel/babel/issues/4270\n          //\n          // eslint-disable-next-line prefer-spread\n          return player[functionName].apply(player, args);\n        });\n      };\n    }\n  };\n\n  var _iteratorNormalCompletion2 = true;\n  var _didIteratorError2 = false;\n  var _iteratorError2 = undefined;\n\n  try {\n    for (var _iterator2 = _functionNames2.default[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n      var functionName = _step2.value;\n\n      _loop2(functionName);\n    }\n  } catch (err) {\n    _didIteratorError2 = true;\n    _iteratorError2 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion2 && _iterator2.return) {\n        _iterator2.return();\n      }\n    } finally {\n      if (_didIteratorError2) {\n        throw _iteratorError2;\n      }\n    }\n  }\n\n  return functions;\n};\n\nexports.default = YouTubePlayer;\nmodule.exports = exports['default'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _sister = require('sister');\n\nvar _sister2 = _interopRequireDefault(_sister);\n\nvar _loadYouTubeIframeApi = require('./loadYouTubeIframeApi');\n\nvar _loadYouTubeIframeApi2 = _interopRequireDefault(_loadYouTubeIframeApi);\n\nvar _YouTubePlayer = require('./YouTubePlayer');\n\nvar _YouTubePlayer2 = _interopRequireDefault(_YouTubePlayer);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @typedef YT.Player\n * @see https://developers.google.com/youtube/iframe_api_reference\n * */\n\n/**\n * @see https://developers.google.com/youtube/iframe_api_reference#Loading_a_Video_Player\n */\nvar youtubeIframeAPI = void 0;\n\n/**\n * A factory function used to produce an instance of YT.Player and queue function calls and proxy events of the resulting object.\n *\n * @param maybeElementId Either An existing YT.Player instance,\n * the DOM element or the id of the HTML element where the API will insert an <iframe>.\n * @param options See `options` (Ignored when using an existing YT.Player instance).\n * @param strictState A flag designating whether or not to wait for\n * an acceptable state when calling supported functions. Default: `false`.\n * See `FunctionStateMap.js` for supported functions and acceptable states.\n */\n\nexports.default = function (maybeElementId) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var strictState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var emitter = (0, _sister2.default)();\n\n  if (!youtubeIframeAPI) {\n    youtubeIframeAPI = (0, _loadYouTubeIframeApi2.default)(emitter);\n  }\n\n  if (options.events) {\n    throw new Error('Event handlers cannot be overwritten.');\n  }\n\n  if (typeof maybeElementId === 'string' && !document.getElementById(maybeElementId)) {\n    throw new Error('Element \"' + maybeElementId + '\" does not exist.');\n  }\n\n  options.events = _YouTubePlayer2.default.proxyEvents(emitter);\n\n  var playerAPIReady = new Promise(function (resolve) {\n    if ((typeof maybeElementId === 'undefined' ? 'undefined' : _typeof(maybeElementId)) === 'object' && maybeElementId.playVideo instanceof Function) {\n      var player = maybeElementId;\n\n      resolve(player);\n    } else {\n      // asume maybeElementId can be rendered inside\n      // eslint-disable-next-line promise/catch-or-return\n      youtubeIframeAPI.then(function (YT) {\n        // eslint-disable-line promise/prefer-await-to-then\n        var player = new YT.Player(maybeElementId, options);\n\n        emitter.on('ready', function () {\n          resolve(player);\n        });\n\n        return null;\n      });\n    }\n  });\n\n  var playerApi = _YouTubePlayer2.default.promisifyPlayer(playerAPIReady, strictState);\n\n  playerApi.on = emitter.on;\n  playerApi.off = emitter.off;\n\n  return playerApi;\n};\n\nmodule.exports = exports['default'];", "/** @jsxRuntime classic */\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport isEqual from 'fast-deep-equal';\nimport youTubePlayer from 'youtube-player';\nimport type { YouTubePlayer, Options } from 'youtube-player/dist/types';\n\n/**\n * Check whether a `props` change should result in the video being updated.\n */\nfunction shouldUpdateVideo(prevProps: YouTubeProps, props: YouTubeProps) {\n  // A changing video should always trigger an update\n  if (prevProps.videoId !== props.videoId) {\n    return true;\n  }\n\n  // Otherwise, a change in the start/end time playerVars also requires a player\n  // update.\n  const prevVars = prevProps.opts?.playerVars || {};\n  const vars = props.opts?.playerVars || {};\n\n  return prevVars.start !== vars.start || prevVars.end !== vars.end;\n}\n\n/**\n * Neutralize API options that only require a video update, leaving only options\n * that require a player reset. The results can then be compared to see if a\n * player reset is necessary.\n */\nfunction filterResetOptions(opts: Options = {}) {\n  return {\n    ...opts,\n    height: 0,\n    width: 0,\n    playerVars: {\n      ...opts.playerVars,\n      autoplay: 0,\n      start: 0,\n      end: 0,\n    },\n  };\n}\n\n/**\n * Check whether a `props` change should result in the player being reset.\n * The player is reset when the `props.opts` change, except if the only change\n * is in the `start` and `end` playerVars, because a video update can deal with\n * those.\n */\nfunction shouldResetPlayer(prevProps: YouTubeProps, props: YouTubeProps) {\n  return (\n    prevProps.videoId !== props.videoId || !isEqual(filterResetOptions(prevProps.opts), filterResetOptions(props.opts))\n  );\n}\n\n/**\n * Check whether a props change should result in an update of player.\n */\nfunction shouldUpdatePlayer(prevProps: YouTubeProps, props: YouTubeProps) {\n  return (\n    prevProps.id !== props.id ||\n    prevProps.className !== props.className ||\n    prevProps.opts?.width !== props.opts?.width ||\n    prevProps.opts?.height !== props.opts?.height ||\n    prevProps.iframeClassName !== props.iframeClassName ||\n    prevProps.title !== props.title\n  );\n}\n\ntype YoutubePlayerCueVideoOptions = {\n  videoId: string;\n  startSeconds?: number;\n  endSeconds?: number;\n  suggestedQuality?: string;\n};\n\nexport { YouTubePlayer };\n\nexport type YouTubeEvent<T = any> = {\n  data: T;\n  target: YouTubePlayer;\n};\n\nexport type YouTubeProps = {\n  /**\n   * The YouTube video ID.\n   *\n   * Examples\n   * - https://www.youtube.com/watch?v=XxVg_s8xAms (`XxVg_s8xAms` is the ID)\n   * - https://www.youtube.com/embed/-DX3vJiqxm4 (`-DX3vJiqxm4` is the ID)\n   */\n  videoId?: string;\n  /**\n   * Custom ID for the player element\n   */\n  id?: string;\n  /**\n   * Custom class name for the player element\n   */\n  className?: string;\n  /**\n   * Custom class name for the iframe element\n   */\n  iframeClassName?: string;\n  /**\n   * Custom style for the player container element\n   */\n  style?: React.CSSProperties;\n  /**\n   * Title of the video for the iframe's title tag.\n   */\n  title?: React.IframeHTMLAttributes<HTMLIFrameElement>['title'];\n  /**\n   * Indicates how the browser should load the iframe\n   * {@link https://developer.mozilla.org/en-US/docs/Web/HTML/Element/iframe#attr-loading}\n   */\n  loading?: React.IframeHTMLAttributes<HTMLIFrameElement>['loading'];\n  /**\n   * An object that specifies player options\n   * {@link https://developers.google.com/youtube/iframe_api_reference#Loading_a_Video_Player}\n   */\n  opts?: Options;\n  /**\n   * This event fires whenever a player has finished loading and is ready to begin receiving API calls.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onReady}\n   */\n  onReady?: (event: YouTubeEvent) => void;\n  /**\n   * This event fires if an error occurs in the player.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onError}\n   */\n  onError?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.PLAYING.\n   */\n  onPlay?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.PAUSED.\n   */\n  onPause?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.ENDED.\n   */\n  onEnd?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the player's state changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onStateChange}\n   */\n  onStateChange?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the video playback quality changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onPlaybackRateChange}\n   */\n  onPlaybackRateChange?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the video playback rate changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onPlaybackQualityChange}\n   */\n  onPlaybackQualityChange?: (event: YouTubeEvent<string>) => void;\n};\n\nconst defaultProps: YouTubeProps = {\n  videoId: '',\n  id: '',\n  className: '',\n  iframeClassName: '',\n  style: {},\n  title: '',\n  loading: undefined,\n  opts: {},\n  onReady: () => {},\n  onError: () => {},\n  onPlay: () => {},\n  onPause: () => {},\n  onEnd: () => {},\n  onStateChange: () => {},\n  onPlaybackRateChange: () => {},\n  onPlaybackQualityChange: () => {},\n};\n\nconst propTypes = {\n  videoId: PropTypes.string,\n  id: PropTypes.string,\n  className: PropTypes.string,\n  iframeClassName: PropTypes.string,\n  style: PropTypes.object,\n  title: PropTypes.string,\n  loading: PropTypes.oneOf(['lazy', 'eager']),\n  opts: PropTypes.objectOf(PropTypes.any),\n  onReady: PropTypes.func,\n  onError: PropTypes.func,\n  onPlay: PropTypes.func,\n  onPause: PropTypes.func,\n  onEnd: PropTypes.func,\n  onStateChange: PropTypes.func,\n  onPlaybackRateChange: PropTypes.func,\n  onPlaybackQualityChange: PropTypes.func,\n};\n\nclass YouTube extends React.Component<YouTubeProps> {\n  static propTypes = propTypes;\n  static defaultProps = defaultProps;\n\n  /**\n   * Expose PlayerState constants for convenience. These constants can also be\n   * accessed through the global YT object after the YouTube IFrame API is instantiated.\n   * https://developers.google.com/youtube/iframe_api_reference#onStateChange\n   */\n  static PlayerState = {\n    UNSTARTED: -1,\n    ENDED: 0,\n    PLAYING: 1,\n    PAUSED: 2,\n    BUFFERING: 3,\n    CUED: 5,\n  };\n\n  container: HTMLDivElement | null;\n  internalPlayer: YouTubePlayer | null;\n\n  constructor(props: any) {\n    super(props);\n\n    this.container = null;\n    this.internalPlayer = null;\n  }\n\n  /**\n   * Note: The `youtube-player` package that is used promisifies all YouTube\n   * Player API calls, which introduces a delay of a tick before it actually\n   * gets destroyed.\n   *\n   * The promise to destroy the player is stored here so we can make sure to\n   * only re-create the Player after it's been destroyed.\n   *\n   * See: https://github.com/tjallingt/react-youtube/issues/355\n   */\n  destroyPlayerPromise: Promise<void> | undefined = undefined;\n\n  componentDidMount() {\n    this.createPlayer();\n  }\n\n  async componentDidUpdate(prevProps: YouTubeProps) {\n    if (shouldUpdatePlayer(prevProps, this.props)) {\n      this.updatePlayer();\n    }\n\n    if (shouldResetPlayer(prevProps, this.props)) {\n      await this.resetPlayer();\n    }\n\n    if (shouldUpdateVideo(prevProps, this.props)) {\n      this.updateVideo();\n    }\n  }\n\n  componentWillUnmount() {\n    this.destroyPlayer();\n  }\n\n  /**\n   * This event fires whenever a player has finished loading and is ready to begin receiving API calls.\n   * https://developers.google.com/youtube/iframe_api_reference#onReady\n   */\n  onPlayerReady = (event: YouTubeEvent) => this.props.onReady?.(event);\n\n  /**\n   * This event fires if an error occurs in the player.\n   * https://developers.google.com/youtube/iframe_api_reference#onError\n   */\n  onPlayerError = (event: YouTubeEvent<number>) => this.props.onError?.(event);\n\n  /**\n   * This event fires whenever the video playback quality changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onStateChange\n   */\n  onPlayerStateChange = (event: YouTubeEvent<number>) => {\n    this.props.onStateChange?.(event);\n    // @ts-ignore\n    switch (event.data) {\n      case YouTube.PlayerState.ENDED:\n        this.props.onEnd?.(event);\n        break;\n\n      case YouTube.PlayerState.PLAYING:\n        this.props.onPlay?.(event);\n        break;\n\n      case YouTube.PlayerState.PAUSED:\n        this.props.onPause?.(event);\n        break;\n\n      default:\n    }\n  };\n\n  /**\n   * This event fires whenever the video playback quality changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onPlaybackRateChange\n   */\n  onPlayerPlaybackRateChange = (event: YouTubeEvent<number>) => this.props.onPlaybackRateChange?.(event);\n\n  /**\n   * This event fires whenever the video playback rate changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onPlaybackQualityChange\n   */\n  onPlayerPlaybackQualityChange = (event: YouTubeEvent<string>) => this.props.onPlaybackQualityChange?.(event);\n\n  /**\n   * Destroy the YouTube Player using its async API and store the promise so we\n   * can await before re-creating it.\n   */\n  destroyPlayer = () => {\n    if (this.internalPlayer) {\n      this.destroyPlayerPromise = this.internalPlayer.destroy().then(() => (this.destroyPlayerPromise = undefined));\n      return this.destroyPlayerPromise;\n    }\n    return Promise.resolve();\n  };\n\n  /**\n   * Initialize the YouTube Player API on the container and attach event handlers\n   */\n  createPlayer = () => {\n    // do not attempt to create a player server-side, it won't work\n    if (typeof document === 'undefined') return;\n    if (this.destroyPlayerPromise) {\n      // We need to first await the existing player to be destroyed before\n      // we can re-create it.\n      this.destroyPlayerPromise.then(this.createPlayer);\n      return;\n    }\n    // create player\n    const playerOpts: Options = {\n      ...this.props.opts,\n      // preload the `videoId` video if one is already given\n      videoId: this.props.videoId,\n    };\n    this.internalPlayer = youTubePlayer(this.container!, playerOpts);\n    // attach event handlers\n    this.internalPlayer.on('ready', this.onPlayerReady as any);\n    this.internalPlayer.on('error', this.onPlayerError as any);\n    this.internalPlayer.on('stateChange', this.onPlayerStateChange as any);\n    this.internalPlayer.on('playbackRateChange', this.onPlayerPlaybackRateChange as any);\n    this.internalPlayer.on('playbackQualityChange', this.onPlayerPlaybackQualityChange as any);\n    if (this.props.title || this.props.loading) {\n      this.internalPlayer.getIframe().then((iframe) => {\n        if (this.props.title) iframe.setAttribute('title', this.props.title);\n        if (this.props.loading) iframe.setAttribute('loading', this.props.loading);\n      });\n    }\n  };\n\n  /**\n   * Shorthand for destroying and then re-creating the YouTube Player\n   */\n  resetPlayer = () => this.destroyPlayer().then(this.createPlayer);\n\n  /**\n   * Method to update the id and class of the YouTube Player iframe.\n   * React should update this automatically but since the YouTube Player API\n   * replaced the DIV that is mounted by React we need to do this manually.\n   */\n  updatePlayer = () => {\n    this.internalPlayer?.getIframe().then((iframe) => {\n      if (this.props.id) iframe.setAttribute('id', this.props.id);\n      else iframe.removeAttribute('id');\n      if (this.props.iframeClassName) iframe.setAttribute('class', this.props.iframeClassName);\n      else iframe.removeAttribute('class');\n      if (this.props.opts && this.props.opts.width) iframe.setAttribute('width', this.props.opts.width.toString());\n      else iframe.removeAttribute('width');\n      if (this.props.opts && this.props.opts.height) iframe.setAttribute('height', this.props.opts.height.toString());\n      else iframe.removeAttribute('height');\n      if (this.props.title) iframe.setAttribute('title', this.props.title);\n      else iframe.setAttribute('title', 'YouTube video player');\n      if (this.props.loading) iframe.setAttribute('loading', this.props.loading);\n      else iframe.removeAttribute('loading');\n    });\n  };\n\n  /**\n   *  Method to return the internalPlayer object.\n   */\n  getInternalPlayer = () => {\n    return this.internalPlayer;\n  };\n\n  /**\n   * Call YouTube Player API methods to update the currently playing video.\n   * Depending on the `opts.playerVars.autoplay` this function uses one of two\n   * YouTube Player API methods to update the video.\n   */\n  updateVideo = () => {\n    if (typeof this.props.videoId === 'undefined' || this.props.videoId === null) {\n      this.internalPlayer?.stopVideo();\n      return;\n    }\n\n    // set queueing options\n    let autoplay = false;\n    const opts: YoutubePlayerCueVideoOptions = {\n      videoId: this.props.videoId,\n    };\n\n    if (this.props.opts?.playerVars) {\n      autoplay = this.props.opts.playerVars.autoplay === 1;\n      if ('start' in this.props.opts.playerVars) {\n        opts.startSeconds = this.props.opts.playerVars.start;\n      }\n      if ('end' in this.props.opts.playerVars) {\n        opts.endSeconds = this.props.opts.playerVars.end;\n      }\n    }\n\n    // if autoplay is enabled loadVideoById\n    if (autoplay) {\n      this.internalPlayer?.loadVideoById(opts);\n      return;\n    }\n    // default behaviour just cues the video\n    this.internalPlayer?.cueVideoById(opts);\n  };\n\n  refContainer = (container: HTMLDivElement) => {\n    this.container = container;\n  };\n\n  render() {\n    return (\n      <div className={this.props.className} style={this.props.style}>\n        <div id={this.props.id} className={this.props.iframeClassName} ref={this.refContainer} />\n      </div>\n    );\n  }\n}\n\nexport default YouTube;\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,gBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAAS,GAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASA,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;ACZN;AAAA;AAAA;AAMA,WAAO,UAAU,SAAS,MAAM,GAAG,GAAG;AACpC,UAAI,MAAM;AAAG,eAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE;AAAa,iBAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE;AAAQ,mBAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,qBAAO;AACjC,iBAAO;AAAA,QACT;AAIA,YAAI,EAAE,gBAAgB;AAAQ,iBAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,YAAI,EAAE,YAAY,OAAO,UAAU;AAAS,iBAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAC7E,YAAI,EAAE,aAAa,OAAO,UAAU;AAAU,iBAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAEjF,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,iBAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC;AAAG,mBAAO;AAEhE,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,cAAI,MAAM,KAAK,CAAC;AAEhB,cAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAG,mBAAO;AAAA,QACrC;AAEA,eAAO;AAAA,MACT;AAGA,aAAO,MAAI,KAAK,MAAI;AAAA,IACtB;AAAA;AAAA;;;AC7CA;AAAA;AAAA;AAEA,QAAI;AAMJ,aAAS,WAAY;AACjB,UAAI,SAAS,CAAC,GACV,SAAS,CAAC;AAad,aAAO,KAAK,SAAU,MAAM,SAAS;AACjC,YAAI,WAAW,EAAC,MAAY,QAAgB;AAC5C,eAAO,IAAI,IAAI,OAAO,IAAI,KAAK,CAAC;AAChC,eAAO,IAAI,EAAE,QAAQ,QAAQ;AAC7B,eAAO;AAAA,MACX;AAKA,aAAO,MAAM,SAAU,UAAU;AAC7B,YAAI,QAAQ,OAAO,SAAS,IAAI,EAAE,QAAQ,QAAQ;AAElD,YAAI,UAAU,IAAI;AACd,iBAAO,SAAS,IAAI,EAAE,OAAO,OAAO,CAAC;AAAA,QACzC;AAAA,MACJ;AAMA,aAAO,UAAU,SAAU,MAAM,MAAM;AACnC,YAAI,YAAY,OAAO,IAAI,GACvB;AAEJ,YAAI,WAAW;AACX,cAAI,UAAU;AACd,iBAAO,KAAK;AACR,sBAAU,CAAC,EAAE,QAAQ,IAAI;AAAA,UAC7B;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5DjB;AAAA;AACA,WAAO,UAAU,SAAS,KAAM,KAAK,MAAM,IAAI;AAC7C,UAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,UAAI,SAAS,SAAS,cAAc,QAAQ;AAE5C,UAAI,OAAO,SAAS,YAAY;AAC9B,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,aAAO,QAAQ,CAAC;AAChB,WAAK,MAAM,WAAW;AAAA,MAAC;AAEvB,aAAO,OAAO,KAAK,QAAQ;AAC3B,aAAO,UAAU,KAAK,WAAW;AACjC,aAAO,QAAQ,WAAW,OAAO,CAAC,CAAC,KAAK,QAAQ;AAChD,aAAO,MAAM;AAEb,UAAI,KAAK,OAAO;AACd,sBAAc,QAAQ,KAAK,KAAK;AAAA,MAClC;AAEA,UAAI,KAAK,MAAM;AACb,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B;AAEA,UAAI,QAAQ,YAAY,SAAS,WAAW;AAC5C,YAAM,QAAQ,EAAE;AAKhB,UAAI,CAAC,OAAO,QAAQ;AAClB,iBAAS,QAAQ,EAAE;AAAA,MACrB;AAEA,WAAK,YAAY,MAAM;AAAA,IACzB;AAEA,aAAS,cAAc,QAAQ,OAAO;AACpC,eAAS,QAAQ,OAAO;AACtB,eAAO,aAAa,MAAM,MAAM,IAAI,CAAC;AAAA,MACvC;AAAA,IACF;AAEA,aAAS,SAAU,QAAQ,IAAI;AAC7B,aAAO,SAAS,WAAY;AAC1B,aAAK,UAAU,KAAK,SAAS;AAC7B,WAAG,MAAM,MAAM;AAAA,MACjB;AACA,aAAO,UAAU,WAAY;AAG3B,aAAK,UAAU,KAAK,SAAS;AAC7B,WAAG,IAAI,MAAM,oBAAoB,KAAK,GAAG,GAAG,MAAM;AAAA,MACpD;AAAA,IACF;AAEA,aAAS,QAAS,QAAQ,IAAI;AAC5B,aAAO,qBAAqB,WAAY;AACtC,YAAI,KAAK,cAAc,cAAc,KAAK,cAAc;AAAU;AAClE,aAAK,qBAAqB;AAC1B,WAAG,MAAM,MAAM;AAAA,MACjB;AAAA,IACF;AAAA;AAAA;;;AChEA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,cAAc;AAElB,QAAI,eAAe,uBAAuB,WAAW;AAErD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU,SAAU,SAAS;AAKnC,UAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS;AAClD,YAAI,OAAO,MAAM,OAAO,GAAG,UAAU,OAAO,GAAG,kBAAkB,UAAU;AACzE,kBAAQ,OAAO,EAAE;AAEjB;AAAA,QACF,OAAO;AACL,cAAI,WAAW,OAAO,SAAS,aAAa,UAAU,UAAU;AAEhE,WAAC,GAAG,aAAa,SAAS,WAAW,gCAAgC,SAAU,OAAO;AACpF,gBAAI,OAAO;AACT,sBAAQ,QAAQ,SAAS,KAAK;AAAA,YAChC;AAAA,UACF,CAAC;AAAA,QACH;AAEA,YAAI,WAAW,OAAO;AAItB,eAAO,0BAA0B,WAAY;AAC3C,cAAI,UAAU;AACZ,qBAAS;AAAA,UACX;AAEA,kBAAQ,OAAO,EAAE;AAAA,QACnB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;AChDlC;AAAA;AAIA,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AAgBZ,WAAO,UAAU,SAAS,KAAK,SAAS;AACtC,gBAAU,WAAW,CAAC;AACtB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,YAAY,IAAI,SAAS,GAAG;AACvC,eAAO,MAAM,GAAG;AAAA,MAClB,WAAW,SAAS,YAAY,MAAM,GAAG,MAAM,OAAO;AACpD,eAAO,QAAQ,OAAO,QAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,MACnD;AACA,YAAM,IAAI;AAAA,QACR,0DACE,KAAK,UAAU,GAAG;AAAA,MACtB;AAAA,IACF;AAUA,aAAS,MAAM,KAAK;AAClB,YAAM,OAAO,GAAG;AAChB,UAAI,IAAI,SAAS,KAAK;AACpB;AAAA,MACF;AACA,UAAI,QAAQ,wHAAwH;AAAA,QAClI;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,IAAI,WAAW,MAAM,CAAC,CAAC;AAC3B,UAAI,QAAQ,MAAM,CAAC,KAAK,MAAM,YAAY;AAC1C,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,aAAS,SAAS,IAAI;AACpB,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK;AAAA,IACd;AAUA,aAAS,QAAQ,IAAI;AACnB,aAAO,OAAO,IAAI,GAAG,KAAK,KACxB,OAAO,IAAI,GAAG,MAAM,KACpB,OAAO,IAAI,GAAG,QAAQ,KACtB,OAAO,IAAI,GAAG,QAAQ,KACtB,KAAK;AAAA,IACT;AAMA,aAAS,OAAO,IAAI,GAAG,MAAM;AAC3B,UAAI,KAAK,GAAG;AACV;AAAA,MACF;AACA,UAAI,KAAK,IAAI,KAAK;AAChB,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM;AAAA,MACpC;AACA,aAAO,KAAK,KAAK,KAAK,CAAC,IAAI,MAAM,OAAO;AAAA,IAC1C;AAAA;AAAA;;;ACvJA;AAAA;AAQA,cAAU,OAAO,UAAU,YAAY,QAAQ,YAAY,SAAS,IAAI;AACxE,YAAQ,SAAS;AACjB,YAAQ,UAAU;AAClB,YAAQ,SAAS;AACjB,YAAQ,UAAU;AAClB,YAAQ,WAAW;AAMnB,YAAQ,QAAQ,CAAC;AACjB,YAAQ,QAAQ,CAAC;AAQjB,YAAQ,aAAa,CAAC;AAMtB,QAAI;AASJ,aAAS,YAAY,WAAW;AAC9B,UAAI,OAAO,GAAG;AAEd,WAAK,KAAK,WAAW;AACnB,gBAAU,QAAQ,KAAK,OAAQ,UAAU,WAAW,CAAC;AACrD,gBAAQ;AAAA,MACV;AAEA,aAAO,QAAQ,OAAO,KAAK,IAAI,IAAI,IAAI,QAAQ,OAAO,MAAM;AAAA,IAC9D;AAUA,aAAS,YAAY,WAAW;AAE9B,eAAS,QAAQ;AAEf,YAAI,CAAC,MAAM;AAAS;AAEpB,YAAI,OAAO;AAGX,YAAI,OAAO,CAAC,oBAAI,KAAK;AACrB,YAAI,KAAK,QAAQ,YAAY;AAC7B,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,mBAAW;AAGX,YAAI,OAAO,IAAI,MAAM,UAAU,MAAM;AACrC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,CAAC,IAAI,UAAU,CAAC;AAAA,QACvB;AAEA,aAAK,CAAC,IAAI,QAAQ,OAAO,KAAK,CAAC,CAAC;AAEhC,YAAI,aAAa,OAAO,KAAK,CAAC,GAAG;AAE/B,eAAK,QAAQ,IAAI;AAAA,QACnB;AAGA,YAAI,QAAQ;AACZ,aAAK,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,iBAAiB,SAAS,OAAO,QAAQ;AAEjE,cAAI,UAAU;AAAM,mBAAO;AAC3B;AACA,cAAI,YAAY,QAAQ,WAAW,MAAM;AACzC,cAAI,eAAe,OAAO,WAAW;AACnC,gBAAI,MAAM,KAAK,KAAK;AACpB,oBAAQ,UAAU,KAAK,MAAM,GAAG;AAGhC,iBAAK,OAAO,OAAO,CAAC;AACpB;AAAA,UACF;AACA,iBAAO;AAAA,QACT,CAAC;AAGD,gBAAQ,WAAW,KAAK,MAAM,IAAI;AAElC,YAAI,QAAQ,MAAM,OAAO,QAAQ,OAAO,QAAQ,IAAI,KAAK,OAAO;AAChE,cAAM,MAAM,MAAM,IAAI;AAAA,MACxB;AAEA,YAAM,YAAY;AAClB,YAAM,UAAU,QAAQ,QAAQ,SAAS;AACzC,YAAM,YAAY,QAAQ,UAAU;AACpC,YAAM,QAAQ,YAAY,SAAS;AAGnC,UAAI,eAAe,OAAO,QAAQ,MAAM;AACtC,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,OAAO,YAAY;AAC1B,cAAQ,KAAK,UAAU;AAEvB,cAAQ,QAAQ,CAAC;AACjB,cAAQ,QAAQ,CAAC;AAEjB,UAAI,SAAS,OAAO,eAAe,WAAW,aAAa,IAAI,MAAM,QAAQ;AAC7E,UAAI,MAAM,MAAM;AAEhB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,CAAC,MAAM,CAAC;AAAG;AACf,qBAAa,MAAM,CAAC,EAAE,QAAQ,OAAO,KAAK;AAC1C,YAAI,WAAW,CAAC,MAAM,KAAK;AACzB,kBAAQ,MAAM,KAAK,IAAI,OAAO,MAAM,WAAW,OAAO,CAAC,IAAI,GAAG,CAAC;AAAA,QACjE,OAAO;AACL,kBAAQ,MAAM,KAAK,IAAI,OAAO,MAAM,aAAa,GAAG,CAAC;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAQA,aAAS,UAAU;AACjB,cAAQ,OAAO,EAAE;AAAA,IACnB;AAUA,aAAS,QAAQ,MAAM;AACrB,UAAI,GAAG;AACP,WAAK,IAAI,GAAG,MAAM,QAAQ,MAAM,QAAQ,IAAI,KAAK,KAAK;AACpD,YAAI,QAAQ,MAAM,CAAC,EAAE,KAAK,IAAI,GAAG;AAC/B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,IAAI,GAAG,MAAM,QAAQ,MAAM,QAAQ,IAAI,KAAK,KAAK;AACpD,YAAI,QAAQ,MAAM,CAAC,EAAE,KAAK,IAAI,GAAG;AAC/B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAUA,aAAS,OAAO,KAAK;AACnB,UAAI,eAAe;AAAO,eAAO,IAAI,SAAS,IAAI;AAClD,aAAO;AAAA,IACT;AAAA;AAAA;;;ACzMA;AAAA;AAMA,cAAU,OAAO,UAAU;AAC3B,YAAQ,MAAM;AACd,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,YAAY;AACpB,YAAQ,UAAU,eAAe,OAAO,UACtB,eAAe,OAAO,OAAO,UAC3B,OAAO,QAAQ,QACf,aAAa;AAMjC,YAAQ,SAAS;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAUA,aAAS,YAAY;AAInB,UAAI,OAAO,WAAW,eAAe,OAAO,WAAW,OAAO,QAAQ,SAAS,YAAY;AACzF,eAAO;AAAA,MACT;AAIA,aAAQ,OAAO,aAAa,eAAe,SAAS,mBAAmB,SAAS,gBAAgB,SAAS,SAAS,gBAAgB,MAAM;AAAA,MAErI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,WAAY,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA;AAAA,MAG1H,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,KAAK,SAAS,OAAO,IAAI,EAAE,KAAK;AAAA,MAEnJ,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,IAC5H;AAMA,YAAQ,WAAW,IAAI,SAAS,GAAG;AACjC,UAAI;AACF,eAAO,KAAK,UAAU,CAAC;AAAA,MACzB,SAAS,KAAK;AACZ,eAAO,iCAAiC,IAAI;AAAA,MAC9C;AAAA,IACF;AASA,aAAS,WAAW,MAAM;AACxB,UAAIC,aAAY,KAAK;AAErB,WAAK,CAAC,KAAKA,aAAY,OAAO,MAC1B,KAAK,aACJA,aAAY,QAAQ,OACrB,KAAK,CAAC,KACLA,aAAY,QAAQ,OACrB,MAAM,QAAQ,SAAS,KAAK,IAAI;AAEpC,UAAI,CAACA;AAAW;AAEhB,UAAI,IAAI,YAAY,KAAK;AACzB,WAAK,OAAO,GAAG,GAAG,GAAG,gBAAgB;AAKrC,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,WAAK,CAAC,EAAE,QAAQ,eAAe,SAAS,OAAO;AAC7C,YAAI,SAAS;AAAO;AACpB;AACA,YAAI,SAAS,OAAO;AAGlB,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAED,WAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IACzB;AASA,aAAS,MAAM;AAGb,aAAO,aAAa,OAAO,WACtB,QAAQ,OACR,SAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,SAAS,SAAS;AAAA,IACpE;AASA,aAAS,KAAK,YAAY;AACxB,UAAI;AACF,YAAI,QAAQ,YAAY;AACtB,kBAAQ,QAAQ,WAAW,OAAO;AAAA,QACpC,OAAO;AACL,kBAAQ,QAAQ,QAAQ;AAAA,QAC1B;AAAA,MACF,SAAQ,GAAG;AAAA,MAAC;AAAA,IACd;AASA,aAAS,OAAO;AACd,UAAI;AACJ,UAAI;AACF,YAAI,QAAQ,QAAQ;AAAA,MACtB,SAAQ,GAAG;AAAA,MAAC;AAGZ,UAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;AAC5D,YAAI,QAAQ,IAAI;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAMA,YAAQ,OAAO,KAAK,CAAC;AAarB,aAAS,eAAe;AACtB,UAAI;AACF,eAAO,OAAO;AAAA,MAChB,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AAAA;AAAA;;;ACxLA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAMD,YAAQ,UAAU,CAAC,gBAAgB,iBAAiB,iBAAiB,kBAAkB,aAAa,cAAc,aAAa,0BAA0B,eAAe,gBAAgB,aAAa,iBAAiB,eAAe,cAAc,WAAW,eAAe,oBAAoB,aAAa,QAAQ,UAAU,WAAW,aAAa,aAAa,UAAU,kBAAkB,mBAAmB,mBAAmB,6BAA6B,sBAAsB,sBAAsB,6BAA6B,kBAAkB,eAAe,uBAAuB,eAAe,qBAAqB,cAAc,aAAa,oBAAoB,WAAW,WAAW,WAAW;AACzrB,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACXlC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAQD,YAAQ,UAAU,CAAC,SAAS,eAAe,yBAAyB,sBAAsB,SAAS,aAAa,cAAc;AAC9H,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACblC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAAA,MAChB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,IACd;AACA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACblC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,gBAAgB;AAEpB,QAAI,iBAAiB,uBAAuB,aAAa;AAEzD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU;AAAA,MAChB,YAAY;AAAA,QACV,kBAAkB,CAAC,eAAe,QAAQ,OAAO,eAAe,QAAQ,MAAM;AAAA,QAC9E,qBAAqB;AAAA,MACvB;AAAA,MACA,WAAW;AAAA,QACT,kBAAkB,CAAC,eAAe,QAAQ,OAAO,eAAe,QAAQ,OAAO;AAAA,QAC/E,qBAAqB;AAAA,MACvB;AAAA,MACA,QAAQ;AAAA,QACN,kBAAkB,CAAC,eAAe,QAAQ,OAAO,eAAe,QAAQ,SAAS,eAAe,QAAQ,MAAM;AAAA,QAC9G,qBAAqB;AAAA;AAAA;AAAA,QAIrB,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;AC9BlC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,iBAAiB;AAErB,QAAI,kBAAkB,uBAAuB,cAAc;AAE3D,QAAI,cAAc;AAElB,QAAI,eAAe,uBAAuB,WAAW;AAErD,QAAI,oBAAoB;AAExB,QAAI,qBAAqB,uBAAuB,iBAAiB;AAEjE,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAI9F,QAAI,SAAS,GAAG,QAAQ,SAAS,gBAAgB;AAEjD,QAAI,gBAAgB,CAAC;AASrB,kBAAc,cAAc,SAAU,SAAS;AAC7C,UAAI,SAAS,CAAC;AAEd,UAAI,QAAQ,SAASC,OAAMC,YAAW;AACpC,YAAI,cAAc,OAAOA,WAAU,MAAM,GAAG,CAAC,EAAE,YAAY,IAAIA,WAAU,MAAM,CAAC;AAEhF,eAAO,WAAW,IAAI,SAAU,OAAO;AACrC,gBAAM,cAAc,aAAa,KAAK;AAEtC,kBAAQ,QAAQA,YAAW,KAAK;AAAA,QAClC;AAAA,MACF;AAEA,UAAI,4BAA4B;AAChC,UAAI,oBAAoB;AACxB,UAAI,iBAAiB;AAErB,UAAI;AACF,iBAAS,YAAY,aAAa,QAAQ,OAAO,QAAQ,EAAE,GAAG,OAAO,EAAE,6BAA6B,QAAQ,UAAU,KAAK,GAAG,OAAO,4BAA4B,MAAM;AACrK,cAAI,YAAY,MAAM;AAEtB,gBAAM,SAAS;AAAA,QACjB;AAAA,MACF,SAAS,KAAK;AACZ,4BAAoB;AACpB,yBAAiB;AAAA,MACnB,UAAE;AACA,YAAI;AACF,cAAI,CAAC,6BAA6B,UAAU,QAAQ;AAClD,sBAAU,OAAO;AAAA,UACnB;AAAA,QACF,UAAE;AACA,cAAI,mBAAmB;AACrB,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAYA,kBAAc,kBAAkB,SAAU,gBAAgB;AACxD,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEtF,UAAI,YAAY,CAAC;AAEjB,UAAI,SAAS,SAASC,QAAOC,eAAc;AACzC,YAAI,eAAe,mBAAmB,QAAQA,aAAY,GAAG;AAC3D,oBAAUA,aAAY,IAAI,WAAY;AACpC,qBAAS,OAAO,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACnF,mBAAK,IAAI,IAAI,UAAU,IAAI;AAAA,YAC7B;AAEA,mBAAO,eAAe,KAAK,SAAU,QAAQ;AAC3C,kBAAI,YAAY,mBAAmB,QAAQA,aAAY;AACvD,kBAAI,cAAc,OAAO,eAAe;AAOxC,kBAAI,QAAQ,OAAOA,aAAY,EAAE,MAAM,QAAQ,IAAI;AAKnD,kBAAI,UAAU;AAAA,cAGd,MAAM,QAAQ,UAAU,gBAAgB,KAAK,UAAU,iBAAiB,QAAQ,WAAW,MAAM,IAAI;AACnG,uBAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,sBAAI,sBAAsB,SAASC,uBAAsB;AACvD,wBAAI,yBAAyB,OAAO,eAAe;AAEnD,wBAAI,UAAU;AAEd,wBAAI,OAAO,UAAU,YAAY,UAAU;AACzC,gCAAU,WAAW,WAAY;AAC/B,+BAAO,oBAAoB,iBAAiBA,oBAAmB;AAE/D,gCAAQ;AAAA,sBACV,GAAG,UAAU,OAAO;AAAA,oBACtB;AAEA,wBAAI,MAAM,QAAQ,UAAU,gBAAgB,KAAK,UAAU,iBAAiB,QAAQ,sBAAsB,MAAM,IAAI;AAClH,6BAAO,oBAAoB,iBAAiBA,oBAAmB;AAE/D,mCAAa,OAAO;AAEpB,8BAAQ;AAAA,oBACV;AAAA,kBACF;AAEA,yBAAO,iBAAiB,iBAAiB,mBAAmB;AAAA,gBAC9D,CAAC,EAAE,KAAK,WAAY;AAClB,yBAAO;AAAA,gBACT,CAAC;AAAA,cACH;AAEA,qBAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,oBAAUD,aAAY,IAAI,WAAY;AACpC,qBAAS,QAAQ,UAAU,QAAQ,OAAO,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACzF,mBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,YAC/B;AAEA,mBAAO,eAAe,KAAK,SAAU,QAAQ;AAM3C,qBAAO,OAAOA,aAAY,EAAE,MAAM,QAAQ,IAAI;AAAA,YAChD,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,UAAI,6BAA6B;AACjC,UAAI,qBAAqB;AACzB,UAAI,kBAAkB;AAEtB,UAAI;AACF,iBAAS,aAAa,gBAAgB,QAAQ,OAAO,QAAQ,EAAE,GAAG,QAAQ,EAAE,8BAA8B,SAAS,WAAW,KAAK,GAAG,OAAO,6BAA6B,MAAM;AAC9K,cAAI,eAAe,OAAO;AAE1B,iBAAO,YAAY;AAAA,QACrB;AAAA,MACF,SAAS,KAAK;AACZ,6BAAqB;AACrB,0BAAkB;AAAA,MACpB,UAAE;AACA,YAAI;AACF,cAAI,CAAC,8BAA8B,WAAW,QAAQ;AACpD,uBAAW,OAAO;AAAA,UACpB;AAAA,QACF,UAAE;AACA,cAAI,oBAAoB;AACtB,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU;AAClB,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACrMlC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAK,IAAI,SAAU,KAAK;AAAE,aAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,IAAK;AAE3Q,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,QAAI,wBAAwB;AAE5B,QAAI,yBAAyB,uBAAuB,qBAAqB;AAEzE,QAAI,iBAAiB;AAErB,QAAI,kBAAkB,uBAAuB,cAAc;AAE3D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAU9F,QAAI,mBAAmB;AAavB,YAAQ,UAAU,SAAU,gBAAgB;AAC1C,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEtF,UAAI,WAAW,GAAG,SAAS,SAAS;AAEpC,UAAI,CAAC,kBAAkB;AACrB,4BAAoB,GAAG,uBAAuB,SAAS,OAAO;AAAA,MAChE;AAEA,UAAI,QAAQ,QAAQ;AAClB,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,OAAO,mBAAmB,YAAY,CAAC,SAAS,eAAe,cAAc,GAAG;AAClF,cAAM,IAAI,MAAM,cAAc,iBAAiB,mBAAmB;AAAA,MACpE;AAEA,cAAQ,SAAS,gBAAgB,QAAQ,YAAY,OAAO;AAE5D,UAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS;AAClD,aAAK,OAAO,mBAAmB,cAAc,cAAc,QAAQ,cAAc,OAAO,YAAY,eAAe,qBAAqB,UAAU;AAChJ,cAAI,SAAS;AAEb,kBAAQ,MAAM;AAAA,QAChB,OAAO;AAGL,2BAAiB,KAAK,SAAU,IAAI;AAElC,gBAAIE,UAAS,IAAI,GAAG,OAAO,gBAAgB,OAAO;AAElD,oBAAQ,GAAG,SAAS,WAAY;AAC9B,sBAAQA,OAAM;AAAA,YAChB,CAAC;AAED,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,YAAY,gBAAgB,QAAQ,gBAAgB,gBAAgB,WAAW;AAEnF,gBAAU,KAAK,QAAQ;AACvB,gBAAU,MAAM,QAAQ;AAExB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;AC3FlC,wBAAsB;AACtB,mBAAkB;AAClB,6BAAoB;AACpB,4BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B,SAAS,kBAAkB,WAAyB,OAAqB;AAVzE,MAAA,IAAA;AAYE,MAAI,UAAU,YAAY,MAAM,SAAS;AACvC,WAAO;EACT;AAIA,QAAM,aAAW,KAAA,UAAU,SAAV,OAAA,SAAA,GAAgB,eAAc,CAAC;AAChD,QAAM,SAAO,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,eAAc,CAAC;AAExC,SAAO,SAAS,UAAU,KAAK,SAAS,SAAS,QAAQ,KAAK;AAChE;AAOA,SAAS,mBAAmB,OAAgB,CAAC,GAAG;AAC9C,SAAO,cAAA,eAAA,CAAA,GACF,IAAA,GADE;IAEL,QAAQ;IACR,OAAO;IACP,YAAY,cAAA,eAAA,CAAA,GACP,KAAK,UAAA,GADE;MAEV,UAAU;MACV,OAAO;MACP,KAAK;IACP,CAAA;EACF,CAAA;AACF;AAQA,SAAS,kBAAkB,WAAyB,OAAqB;AACvE,SACE,UAAU,YAAY,MAAM,WAAW,KAAC,uBAAAC,SAAQ,mBAAmB,UAAU,IAAI,GAAG,mBAAmB,MAAM,IAAI,CAAC;AAEtH;AAKA,SAAS,mBAAmB,WAAyB,OAAqB;AA1D1E,MAAA,IAAA,IAAA,IAAA;AA2DE,SACE,UAAU,OAAO,MAAM,MACvB,UAAU,cAAc,MAAM,eAC9B,KAAA,UAAU,SAAV,OAAA,SAAA,GAAgB,aAAU,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,YACtC,KAAA,UAAU,SAAV,OAAA,SAAA,GAAgB,cAAW,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,WACvC,UAAU,oBAAoB,MAAM,mBACpC,UAAU,UAAU,MAAM;AAE9B;AA8FA,IAAM,eAA6B;EACjC,SAAS;EACT,IAAI;EACJ,WAAW;EACX,iBAAiB;EACjB,OAAO,CAAC;EACR,OAAO;EACP,SAAS;EACT,MAAM,CAAC;EACP,SAAS,MAAM;EAAC;EAChB,SAAS,MAAM;EAAC;EAChB,QAAQ,MAAM;EAAC;EACf,SAAS,MAAM;EAAC;EAChB,OAAO,MAAM;EAAC;EACd,eAAe,MAAM;EAAC;EACtB,sBAAsB,MAAM;EAAC;EAC7B,yBAAyB,MAAM;EAAC;AAClC;AAEA,IAAM,YAAY;EAChB,SAAS,kBAAAC,QAAU;EACnB,IAAI,kBAAAA,QAAU;EACd,WAAW,kBAAAA,QAAU;EACrB,iBAAiB,kBAAAA,QAAU;EAC3B,OAAO,kBAAAA,QAAU;EACjB,OAAO,kBAAAA,QAAU;EACjB,SAAS,kBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;EAC1C,MAAM,kBAAAA,QAAU,SAAS,kBAAAA,QAAU,GAAG;EACtC,SAAS,kBAAAA,QAAU;EACnB,SAAS,kBAAAA,QAAU;EACnB,QAAQ,kBAAAA,QAAU;EAClB,SAAS,kBAAAA,QAAU;EACnB,OAAO,kBAAAA,QAAU;EACjB,eAAe,kBAAAA,QAAU;EACzB,sBAAsB,kBAAAA,QAAU;EAChC,yBAAyB,kBAAAA,QAAU;AACrC;AAEA,IAAM,WAAN,cAAsB,aAAAC,QAAM,UAAwB;EAqBlD,YAAY,OAAY;AACtB,UAAM,KAAK;AAgBb,SAAA,uBAAkD;AA4BlD,SAAA,gBAAgB,CAAC,UAAqB;AAzQxC,UAAA,IAAA;AAyQ2C,cAAA,MAAA,KAAA,KAAK,OAAM,YAAX,OAAA,SAAA,GAAA,KAAA,IAAqB,KAAA;IAAA;AAM9D,SAAA,gBAAgB,CAAC,UAA6B;AA/QhD,UAAA,IAAA;AA+QmD,cAAA,MAAA,KAAA,KAAK,OAAM,YAAX,OAAA,SAAA,GAAA,KAAA,IAAqB,KAAA;IAAA;AAMtE,SAAA,sBAAsB,CAAC,UAAgC;AArRzD,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAsRI,OAAA,MAAA,KAAA,KAAK,OAAM,kBAAX,OAAA,SAAA,GAAA,KAAA,IAA2B,KAAA;AAE3B,cAAQ,MAAM,MAAA;QAAA,KACP,SAAQ,YAAY;AACvB,WAAA,MAAA,KAAA,KAAK,OAAM,UAAX,OAAA,SAAA,GAAA,KAAA,IAAmB,KAAA;AACnB;QAAA,KAEG,SAAQ,YAAY;AACvB,WAAA,MAAA,KAAA,KAAK,OAAM,WAAX,OAAA,SAAA,GAAA,KAAA,IAAoB,KAAA;AACpB;QAAA,KAEG,SAAQ,YAAY;AACvB,WAAA,MAAA,KAAA,KAAK,OAAM,YAAX,OAAA,SAAA,GAAA,KAAA,IAAqB,KAAA;AACrB;QAAA;MAAA;IAIN;AAMA,SAAA,6BAA6B,CAAC,UAA6B;AA7S7D,UAAA,IAAA;AA6SgE,cAAA,MAAA,KAAA,KAAK,OAAM,yBAAX,OAAA,SAAA,GAAA,KAAA,IAAkC,KAAA;IAAA;AAMhG,SAAA,gCAAgC,CAAC,UAA6B;AAnThE,UAAA,IAAA;AAmTmE,cAAA,MAAA,KAAA,KAAK,OAAM,4BAAX,OAAA,SAAA,GAAA,KAAA,IAAqC,KAAA;IAAA;AAMtG,SAAA,gBAAgB,MAAM;AACpB,UAAI,KAAK,gBAAgB;AACvB,aAAK,uBAAuB,KAAK,eAAe,QAAQ,EAAE,KAAK,MAAO,KAAK,uBAAuB,MAAU;AAC5G,eAAO,KAAK;MACd;AACA,aAAO,QAAQ,QAAQ;IACzB;AAKA,SAAA,eAAe,MAAM;AAEnB,UAAI,OAAO,aAAa;AAAa;AACrC,UAAI,KAAK,sBAAsB;AAG7B,aAAK,qBAAqB,KAAK,KAAK,YAAY;AAChD;MACF;AAEA,YAAM,aAAsB,cAAA,eAAA,CAAA,GACvB,KAAK,MAAM,IAAA,GADY;QAG1B,SAAS,KAAK,MAAM;MACtB,CAAA;AACA,WAAK,qBAAiB,sBAAAC,SAAc,KAAK,WAAY,UAAU;AAE/D,WAAK,eAAe,GAAG,SAAS,KAAK,aAAoB;AACzD,WAAK,eAAe,GAAG,SAAS,KAAK,aAAoB;AACzD,WAAK,eAAe,GAAG,eAAe,KAAK,mBAA0B;AACrE,WAAK,eAAe,GAAG,sBAAsB,KAAK,0BAAiC;AACnF,WAAK,eAAe,GAAG,yBAAyB,KAAK,6BAAoC;AACzF,UAAI,KAAK,MAAM,SAAS,KAAK,MAAM,SAAS;AAC1C,aAAK,eAAe,UAAU,EAAE,KAAK,CAAC,WAAW;AAC/C,cAAI,KAAK,MAAM;AAAO,mBAAO,aAAa,SAAS,KAAK,MAAM,KAAK;AACnE,cAAI,KAAK,MAAM;AAAS,mBAAO,aAAa,WAAW,KAAK,MAAM,OAAO;QAC3E,CAAC;MACH;IACF;AAKA,SAAA,cAAc,MAAM,KAAK,cAAc,EAAE,KAAK,KAAK,YAAY;AAO/D,SAAA,eAAe,MAAM;AA5WvB,UAAA;AA6WI,OAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,UAAA,EAAY,KAAK,CAAC,WAAW;AAChD,YAAI,KAAK,MAAM;AAAI,iBAAO,aAAa,MAAM,KAAK,MAAM,EAAE;;AACrD,iBAAO,gBAAgB,IAAI;AAChC,YAAI,KAAK,MAAM;AAAiB,iBAAO,aAAa,SAAS,KAAK,MAAM,eAAe;;AAClF,iBAAO,gBAAgB,OAAO;AACnC,YAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK;AAAO,iBAAO,aAAa,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;;AACtG,iBAAO,gBAAgB,OAAO;AACnC,YAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK;AAAQ,iBAAO,aAAa,UAAU,KAAK,MAAM,KAAK,OAAO,SAAS,CAAC;;AACzG,iBAAO,gBAAgB,QAAQ;AACpC,YAAI,KAAK,MAAM;AAAO,iBAAO,aAAa,SAAS,KAAK,MAAM,KAAK;;AAC9D,iBAAO,aAAa,SAAS,sBAAsB;AACxD,YAAI,KAAK,MAAM;AAAS,iBAAO,aAAa,WAAW,KAAK,MAAM,OAAO;;AACpE,iBAAO,gBAAgB,SAAS;MACvC,CAAA;IACF;AAKA,SAAA,oBAAoB,MAAM;AACxB,aAAO,KAAK;IACd;AAOA,SAAA,cAAc,MAAM;AAzYtB,UAAA,IAAA,IAAA,IAAA;AA0YI,UAAI,OAAO,KAAK,MAAM,YAAY,eAAe,KAAK,MAAM,YAAY,MAAM;AAC5E,SAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,UAAA;AACrB;MACF;AAGA,UAAI,WAAW;AACf,YAAM,OAAqC;QACzC,SAAS,KAAK,MAAM;MACtB;AAEA,WAAI,KAAA,KAAK,MAAM,SAAX,OAAA,SAAA,GAAiB,YAAY;AAC/B,mBAAW,KAAK,MAAM,KAAK,WAAW,aAAa;AACnD,YAAI,WAAW,KAAK,MAAM,KAAK,YAAY;AACzC,eAAK,eAAe,KAAK,MAAM,KAAK,WAAW;QACjD;AACA,YAAI,SAAS,KAAK,MAAM,KAAK,YAAY;AACvC,eAAK,aAAa,KAAK,MAAM,KAAK,WAAW;QAC/C;MACF;AAGA,UAAI,UAAU;AACZ,SAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,cAAc,IAAA;AACnC;MACF;AAEA,OAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,aAAa,IAAA;IACpC;AAEA,SAAA,eAAe,CAAC,cAA8B;AAC5C,WAAK,YAAY;IACnB;AA3ME,SAAK,YAAY;AACjB,SAAK,iBAAiB;EACxB;EAcA,oBAAoB;AAClB,SAAK,aAAa;EACpB;EAEM,mBAAmB,WAAyB;AAAA,WAAA,QAAA,MAAA,MAAA,aAAA;AAChD,UAAI,mBAAmB,WAAW,KAAK,KAAK,GAAG;AAC7C,aAAK,aAAa;MACpB;AAEA,UAAI,kBAAkB,WAAW,KAAK,KAAK,GAAG;AAC5C,cAAM,KAAK,YAAY;MACzB;AAEA,UAAI,kBAAkB,WAAW,KAAK,KAAK,GAAG;AAC5C,aAAK,YAAY;MACnB;IACF,CAAA;EAAA;EAEA,uBAAuB;AACrB,SAAK,cAAc;EACrB;EAyKA,SAAS;AACP,WACE,aAAAD,QAAA,cAAC,OAAA;MAAI,WAAW,KAAK,MAAM;MAAW,OAAO,KAAK,MAAM;IAAA,GACtD,aAAAA,QAAA,cAAC,OAAA;MAAI,IAAI,KAAK,MAAM;MAAI,WAAW,KAAK,MAAM;MAAiB,KAAK,KAAK;IAAA,CAAc,CACzF;EAEJ;AACF;AA5OA,IAAM,UAAN;AAAM,QACG,YAAY;AADf,QAEG,eAAe;AAFlB,QASG,cAAc;EACnB,WAAW;EACX,OAAO;EACP,SAAS;EACT,QAAQ;EACR,WAAW;EACX,MAAM;AACR;AA8NF,IAAO,kBAAQ;", "names": ["i", "checker", "useColors", "_loop", "eventName", "_loop2", "functionName", "onPlayerStateChange", "player", "isEqual", "PropTypes", "React", "youTubePlayer"]}