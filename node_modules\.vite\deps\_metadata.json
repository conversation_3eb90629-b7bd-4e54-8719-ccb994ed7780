{"hash": "f000d6fe", "browserHash": "a87dc0ed", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "81254a5f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "6fc62f92", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "521b8021", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d9c66693", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "4cca8f25", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "c2943415", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.esm.js", "file": "@reduxjs_toolkit.js", "fileHash": "8d78bf9e", "needsInterop": false}, "@reduxjs/toolkit/query/react": {"src": "../../@reduxjs/toolkit/dist/query/react/rtk-query-react.esm.js", "file": "@reduxjs_toolkit_query_react.js", "fileHash": "52368672", "needsInterop": false}, "react-youtube": {"src": "../../react-youtube/dist/YouTube.esm.js", "file": "react-youtube.js", "fileHash": "469d65da", "needsInterop": false}}, "chunks": {"chunk-VRHMX22Y": {"file": "chunk-VRHMX22Y.js"}, "chunk-2UC5YKPU": {"file": "chunk-2UC5YKPU.js"}, "chunk-OXUEMNTN": {"file": "chunk-OXUEMNTN.js"}, "chunk-UXIASGQL": {"file": "chunk-UXIASGQL.js"}}}